<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\Guardian;
use App\Models\Role;
use App\Models\Student;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    const MASTER_DATA_MANAGEMENT = 'master-data';
    const USER_MANAGEMENT = 'user';
    const STUDENT_MANAGEMENT = 'student';
    const CLASS_MANAGEMENT = 'class';
    const COUNSELLING_CASE_RECORD_MANAGEMENT = 'counselling-case-record';
    const CONTRACTOR_MANAGEMENT = 'contractor';
    const EMPLOYEE_MANAGEMENT = 'employee';
    const WALLET_MANAGEMENT = 'wallet';
    const POS_TERMINAL_KEY_MANAGEMENT = 'pos-terminal-key';
    const TERMINAL_MANAGEMENT = 'terminal';
    const SUBJECT_MANAGEMENT = 'subject';
    const CLUB_MANAGEMENT = 'club';
    const GRADING_SCHEME_MANAGEMENT = 'grading-scheme';
    const CONDUCT_SETTING_MANAGEMENT = 'conduct-setting';
    const CONDUCT_RECORD_MANAGEMENT = 'conduct-record';
    const HOSTEL_MANAGEMENT = 'hostel';
    const MERIT_DEMERIT_MANAGEMENT = 'merit-demerit';
    const COMPREHENSIVE_ASSESSMENT_MANAGEMENT = 'comprehensive-assessment';
    const REWARD_PUNISHMENT_MANAGEMENT = 'reward-punishment';
    const LEADERSHIP_POSITION_MANAGEMENT = 'leadership-position';
    const LIBRARY_MANAGEMENT = 'library';
    const ECOMMERCE_MANAGEMENT = 'ecommerce';
    const ROLE_MANAGEMENT = 'role';
    const ANNOUNCEMENT_MANAGEMENT = 'announcement';
    const GUARDIAN_MANAGEMENT = 'guardian';
    const REPORT_MANAGEMENT = 'report';
    const BILLING_DOCUMENT_MANAGEMENT = 'billing-document';
    const SIDE_NAV_MANAGEMENT = 'side-nav';
    const DEADLINE_MANAGEMENT = 'deadline';
    const COMPETITION = 'competition';
    const ACCOUNTING = 'accounting';
    const EXAM_RESULTS_MANAGEMENT = 'exam-results';
    const DISCOUNT_MANAGEMENT = 'discount';
    const STUDENT_SOCIETY_POSITION_MANAGEMENT = 'student-society-position';
    const TIMETABLE_MANAGEMENT = 'timetable-management';
    const UNPAID_ITEM_ASSIGNMENT = 'unpaid-item-assignment';
    const SUBSTITUTE_RECORD_MANAGEMENT = 'substitute-record';
    const SCHOLARSHIP_MANAGEMENT = 'scholarship';
    const GUEST_MANAGEMENT = 'guest';
    const LEAVE_APPLICATION_MANAGEMENT = 'leave-application';
    const ATTENDANCE_MANAGEMENT = 'attendance';
    const CALENDAR_MANAGEMENT = 'calendar';
    const TIMESLOT_OVERRIDE_MANAGEMENT = 'timeslot-override';
    const DUPLICATE_SEMESTER_SETTING_STATUS_MANAGEMENT = 'duplicate-semester-setting-status';

    const SEEDER_PERMISSIONS = [
        self::MASTER_DATA_MANAGEMENT => self::MASTER_DATA_PERMISSIONS,
        self::USER_MANAGEMENT => self::USER_PERMISSIONS,
        self::STUDENT_MANAGEMENT => self::STUDENT_PERMISSIONS,
        self::CLASS_MANAGEMENT => self::CLASS_PERMISSIONS,
        self::COUNSELLING_CASE_RECORD_MANAGEMENT => self::COUNSELLING_CASE_RECORD_PERMISSIONS,
        self::CONTRACTOR_MANAGEMENT => self::CONTRACTOR_PERMISSIONS,
        self::EMPLOYEE_MANAGEMENT => self::EMPLOYEE_PERMISSIONS,
        self::WALLET_MANAGEMENT => self::WALLET_PERMISSIONS,
        self::POS_TERMINAL_KEY_MANAGEMENT => self::POS_TERMINAL_KEY_PERMISSIONS,
        self::TERMINAL_MANAGEMENT => self::TERMINAL_PERMISSIONS,
        self::SUBJECT_MANAGEMENT => self::SUBJECT_PERMISSIONS,
        self::CLUB_MANAGEMENT => self::CLUB_PERMISSIONS,
        self::GRADING_SCHEME_MANAGEMENT => self::GRADING_SCHEME_PERMISSIONS,
        self::CONDUCT_SETTING_MANAGEMENT => self::CONDUCT_SETTING_PERMISSIONS,
        self::CONDUCT_RECORD_MANAGEMENT => self::CONDUCT_RECORD_MANAGEMENT_PERMISSIONS,
        self::HOSTEL_MANAGEMENT => self::HOSTEL_PERMISSIONS,
        self::MERIT_DEMERIT_MANAGEMENT => self::MERIT_DEMERIT_PERMISSIONS,
        self::COMPREHENSIVE_ASSESSMENT_MANAGEMENT => self::COMPREHENSIVE_ASSESSMENT_PERMISSIONS,
        self::REWARD_PUNISHMENT_MANAGEMENT => self::REWARD_PUNISHMENT_PERMISSIONS,
        self::LEADERSHIP_POSITION_MANAGEMENT => self::LEADERSHIP_POSITION_PERMISSIONS,
        self::LIBRARY_MANAGEMENT => self::LIBRARY_PERMISSIONS,
        self::ECOMMERCE_MANAGEMENT => self::ECOMMERCE_PERMISSIONS,
        self::ROLE_MANAGEMENT => self::ROLE_PERMISSIONS,
        self::ANNOUNCEMENT_MANAGEMENT => self::ANNOUNCEMENT_PERMISSIONS,
        self::GUARDIAN_MANAGEMENT => self::GUARDIAN_PERMISSIONS,
        self::REPORT_MANAGEMENT => self::REPORT_PERMISSIONS,
        self::BILLING_DOCUMENT_MANAGEMENT => self::BILLING_DOCUMENT_PERMISSIONS,
        self::SIDE_NAV_MANAGEMENT => self::SIDE_NAV_PERMISSIONS,
        self::DEADLINE_MANAGEMENT => self::DEADLINE_PERMISSIONS,
        self::COMPETITION => self::COMPETITION_PERMISSIONS,
        self::ACCOUNTING => self::ACCOUNTING_PERMISSIONS,
        self::EXAM_RESULTS_MANAGEMENT => self::EXAM_RESULTS_PERMISSIONS,
        self::DISCOUNT_MANAGEMENT => self::DISCOUNT_PERMISSIONS,
        self::STUDENT_SOCIETY_POSITION_MANAGEMENT => self::STUDENT_SOCIETY_POSITION_PERMISSIONS,
        self::TIMETABLE_MANAGEMENT => self::TIMETABLE_MANAGEMENT_PERMISSIONS,
        self::UNPAID_ITEM_ASSIGNMENT => self::UNPAID_ITEM_ASSIGNMENT_PERMISSIONS,
        self::SUBSTITUTE_RECORD_MANAGEMENT => self::SUBSTITUTE_RECORD_PERMISSIONS,
        self::SCHOLARSHIP_MANAGEMENT => self::SCHOLARSHIP_PERMISSIONS,
        self::GUEST_MANAGEMENT => self::GUEST_PERMISSIONS,
        self::LEAVE_APPLICATION_MANAGEMENT => self::LEAVE_APPLICATION_PERMISSIONS,
        self::ATTENDANCE_MANAGEMENT => self::ATTENDANCE_PERMISSIONS,
        self::CALENDAR_MANAGEMENT => self::CALENDAR_PERMISSIONS,
        self::TIMESLOT_OVERRIDE_MANAGEMENT => self::TIMESLOT_OVERRIDE_PERMISSIONS,
        self::DUPLICATE_SEMESTER_SETTING_STATUS_MANAGEMENT => self::DUPLICATE_SEMESTER_SETTING_STATUS_PERMISSIONS,
    ];

    const MASTER_DATA_PERMISSIONS = [
        'master-data-general' => [
            'master-data-menu-view'
        ],
        'master-leave-reason' => [
            'master-leave-reason-view',
            'master-leave-reason-create',
            'master-leave-reason-update',
            'master-leave-reason-delete',
        ],
        'master-employee-category' => [
            'master-employee-category-view'
        ],
        'master-health-concern' => [
            'master-health-concern-view',
            'master-health-concern-create',
            'master-health-concern-update',
            'master-health-concern-delete',
        ],
        'master-bank' => [
            'master-bank-view',
            'master-bank-create',
            'master-bank-update',
            'master-bank-delete',
        ],
        'master-education' => [
            'master-education-view',
            'master-education-create',
            'master-education-update',
            'master-education-delete',
        ],
        'master-course' => [
            'master-course-view',
            'master-course-create',
            'master-course-update',
        ],
        'master-internationalization' => [
            'master-internationalization-view',
            'master-internationalization-create',
            'master-internationalization-update',
        ],
        'master-race' => [
            'master-race-view',
            'master-race-create',
            'master-race-update',
            'master-race-delete',
        ],
        'master-religion' => [
            'master-religion-view',
            'master-religion-create',
            'master-religion-update',
            'master-religion-delete',
        ],
        'master-country' => [
            'master-country-view',
            'master-country-create',
            'master-country-update',
            'master-country-delete',
        ],
        'master-state' => [
            'master-state-view',
            'master-state-create',
            'master-state-update',
            'master-state-delete',
        ],
        'master-school-profile' => [
            'master-school-profile-view',
            'master-school-profile-update',
        ],
        'master-grade' => [
            'master-grade-view',
            'master-grade-create',
            'master-grade-update',
            'master-grade-delete',
        ],
        'master-config' => [
            'master-config-admin-view',
            'master-config-view',
            'master-config-update',
        ],
        'master-semester-year-setting' => [
            'master-semester-year-setting-view',
            'master-semester-year-setting-create',
            'master-semester-year-setting-update',
            'master-semester-year-setting-delete',
        ],
        'master-semester-setting' => [
            'master-semester-setting-view',
            'master-semester-setting-create',
            'master-semester-setting-copy',
            'master-semester-setting-update',
            'master-semester-setting-delete',
        ],
        'master-reward-punishment-category' => [
            'master-reward-punishment-category-view',
            'master-reward-punishment-category-create',
            'master-reward-punishment-category-update',
            'master-reward-punishment-category-delete',
        ],
        'master-society-position' => [
            'master-society-position-view',
            'master-society-position-create',
            'master-society-position-update',
            'master-society-position-delete',
        ],
        'master-reward-punishment-sub-category' => [
            'master-reward-punishment-sub-category-view',
            'master-reward-punishment-sub-category-create',
            'master-reward-punishment-sub-category-update',
            'master-reward-punishment-sub-category-delete',
        ],
        'master-employee-job-title' => [
            'master-employee-job-title-view',
            'master-employee-job-title-create',
            'master-employee-job-title-update',
            'master-employee-job-title-delete',
        ],
        'master-book-sub-classifications' => [
            'master-book-sub-classification-view',
            'master-book-sub-classification-create',
            'master-book-sub-classification-update',
            'master-book-sub-classification-delete',
        ],
        'master-book-source' => [
            'master-book-source-view',
            'master-book-source-create',
            'master-book-source-update',
            'master-book-source-delete',
        ],
        'master-book-classification' => [
            'master-book-classification-view',
            'master-book-classification-create',
            'master-book-classification-update',
            'master-book-classification-delete',
        ],
        'master-book-category' => [
            'master-book-category-view',
            'master-book-category-create',
            'master-book-category-update',
            'master-book-category-delete',
        ],
        'master-author' => [
            'master-author-view',
            'master-author-create',
            'master-author-update',
            'master-author-delete',
        ],
        'master-uom' => [
            'master-uom-view',
        ],
        'master-gl-account' => [
            'master-gl-account-view',
        ],
        'master-currency' => [
            'master-currency-view',
        ],
        'master-product' => [
            'master-product-view',
            'master-product-create',
            'master-product-update',
            'master-product-delete',
        ],
        'master-award' => [
            'master-award-view',
            'master-award-create',
            'master-award-update',
            'master-award-delete',
        ],
        'master-leadership-position' => [
            'master-leadership-position-view',
            'master-leadership-position-create',
            'master-leadership-position-update',
            'master-leadership-position-delete',
        ],
        'master-school' => [
            'master-school-view',
            'master-school-create',
            'master-school-update',
            'master-school-delete',
        ],
        'master-withdrawal-reason' => [
            'master-withdrawal-reason-view',
            'master-withdrawal-reason-create',
            'master-withdrawal-reason-update',
            'master-withdrawal-reason-delete',
        ],
        'master-payment-method-reason' => [
            'master-payment-method-view',
            'master-payment-method-create',
            'master-payment-method-update',
            'master-payment-method-delete',
        ],
        'master-employee-session' => [
            'master-employee-session-view',
            'master-employee-session-create',
            'master-employee-session-update',
            'master-employee-session-delete',
        ],
        'master-department' => [
            'department-view',
            'department-create',
            'department-update',
            'department-delete',
        ],
    ];

    const ECOMMERCE_PERMISSIONS = [
        'merchant' => [
            'merchant-view',
            'merchant-create',
            'merchant-update',
            'merchant-delete',
        ],
        'product' => [
            'product-view',
            'product-create',
            'product-update',
            'product-delete',
            'product-delivery-date-bulk-update',
            'product-available-date-bulk-update'
        ],
        'product-group' => [
            'product-group-view',
            'product-group-create',
            'product-group-update',
            'product-group-delete',
        ],
        'product-category' => [
            'product-category-admin-view',
            'product-category-view',
            'product-category-create',
            'product-category-update',
            'product-category-delete',
        ],
        'product-tag' => [
            'product-tag-view',
            'product-tag-create',
            'product-tag-update',
            'product-tag-delete',
        ],
        'order' => [
            'order-checkout',
            'order-update',
            'order-view',
            'order-admin-view',
            'order-cancel'
        ]
    ];

    const TIMETABLE_MANAGEMENT_PERMISSIONS = [
        'period-group' => [
            'period-group-view',
            'period-group-create',
            'period-group-update',
            'period-group-delete',
        ],
        'period' => [
            'period-view',
            'period-save'
        ],
        'timetable' => [
            'timetable-view',
            'timetable-create',
            'timetable-update',
            'timetable-assign-period-group',
        ],
    ];

    const LIBRARY_PERMISSIONS = [
        'book' => [
            'book-view',
            'book-create',
            'book-update',
            'book-delete',
        ],
        'library-member' => [
            'library-member-view',
            'library-member-create',
            'library-member-update',
            'library-member-delete',
        ],
        'book-loan' => [
            'book-loan-view',
            'book-loan-create',
            'book-loan-return',
            'book-loan-extend',
        ],
        'book-language' => [
            'book-language-view',
        ],
    ];

    const POS_TERMINAL_KEY_PERMISSIONS = [
        'pos-terminal-key' => [
            'pos-terminal-key-view',
            'pos-terminal-key-create',
            'pos-terminal-key-update',
            'pos-terminal-key-delete',
        ],
    ];

    const UNPAID_ITEM_ASSIGNMENT_PERMISSIONS = [
        'unpaid-item-assignment' => [
            'unpaid-item-assignment-admin-view', // For BO
            'unpaid-item-assignment-admin-delete', // For BO
        ],
    ];

    const TERMINAL_PERMISSIONS = [
        'terminal' => [
            'terminal-view',
            'terminal-create',
            'terminal-update',
            'terminal-delete',
        ],
    ];

    const USER_PERMISSIONS = [
        'user-special-setting' => [
            'user-special-setting-view',
            'user-special-setting-create',
            'user-special-setting-update',
            'user-special-setting-delete',
        ],
        'user' => [
            'user-view',
            'user-update',
        ],
    ];

    const SUBJECT_PERMISSIONS = [
        'subject' => [
            'subject-view',
            'subject-create',
            'subject-update',
            'subject-delete',
        ],
        'class-subject' => [
            'primary-class-subject-view',
            'primary-class-subject-create',
            'primary-class-subject-update',
            'primary-class-subject-delete',

            'english-class-subject-view',
            'english-class-subject-create',
            'english-class-subject-update',
            'english-class-subject-delete',

            'society-class-subject-view',
            'society-class-subject-create',
            'society-class-subject-update',
            'society-class-subject-delete',

            'elective-class-subject-view',
            'elective-class-subject-create',
            'elective-class-subject-update',
            'elective-class-subject-delete',
        ],
    ];

    const STUDENT_PERMISSIONS = [
        'student' => [
            'student-view',
            'student-create',
            'student-update',
            'student-delete',
            'student-tab-personal-information-view',
            'student-tab-guardians-view',
            'student-tab-disciplinary-record-view',
            'student-tab-comprehensive-assessment-record-view',
            'student-tab-fee-records-view',
        ],
        'enrollment' => [
            'enrollment-view',
            'enrollment-create',
            'enrollment-update',
            'enrollment-delete',
            'enrollment-upload',
            'enrollment-extend-expiry',
            'enrollment-delete-media',
            'enrollment-manual-payment',
            'enrollment-download-pre-payment-template',
            'enrollment-download-post-payment-template',
            'enrollment-import-template-validation',
            'enrollment-bulk-save-imported-data',
            'enrollment-make-payment',
            'enrollment-import-post-payment-template-validation',
            'enrollment-bulk-save-post-payment-imported-data',
            'enrollment-print-details',
            'enrollment-post-to-autocount',
        ],
        'enrollment-session' => [
            'enrollment-session-view',
            'enrollment-session-create',
            'enrollment-session-update',
            'enrollment-session-delete',
        ],
        'enrollment-user' => [
            'enrollment-user-view',
            'enrollment-user-update',
        ]
    ];

    const CLUB_PERMISSIONS = [
        'club-category' => [
            'club-category-view',
            'club-category-create',
            'club-category-update',
            'club-category-delete',
        ],
        'club' => [
            'club-view',
            'club-create',
            'club-update',
            'club-delete',
        ],
    ];

    const COUNSELLING_CASE_RECORD_PERMISSIONS = [
        'counselling-case-record' => [
            'counselling-case-record-view-all-teacher',
            'counselling-case-record-view',
            'counselling-case-record-create',
            'counselling-case-record-update',
            'counselling-case-record-delete',
        ],
    ];

    const CONTRACTOR_PERMISSIONS = [
        'contractor' => [
            'contractor-view',
            'contractor-create',
            'contractor-update',
            'contractor-delete',
        ],
    ];

    const CLASS_PERMISSIONS = [
        'class' => [
            'primary-class-view',
            'primary-class-create',
            'primary-class-update',
            'primary-class-delete',

            'english-class-view',
            'english-class-create',
            'english-class-update',
            'english-class-delete',

            'society-class-view',
            'society-class-create',
            'society-class-update',
            'society-class-delete',

            'elective-class-view',
            'elective-class-create',
            'elective-class-update',
            'elective-class-delete',

            'class-assignment', // not used (checked both FE and BE)
            'class-seat-assignment-view',
            'class-seat-assignment-update',
        ],
        'semester-class' => [
            'primary-semester-class-view',
            'primary-semester-class-update',
            'primary-semester-class-delete',

            'english-semester-class-view',
            'english-semester-class-update',
            'english-semester-class-delete',

            'society-semester-class-view',
            'society-semester-class-update',
            'society-semester-class-delete',

            'elective-semester-class-view',
            'elective-semester-class-update',
            'elective-semester-class-delete',
        ]
    ];

    const MERIT_DEMERIT_PERMISSIONS = [
        'merit-demerit-setting' => [
            'merit-demerit-setting-view',
            'merit-demerit-setting-create',
            'merit-demerit-setting-update',
            'merit-demerit-setting-delete',
        ],
    ];

    const REWARD_PUNISHMENT_PERMISSIONS = [
        'reward-punishment' => [
            'reward-punishment-view',
            'reward-punishment-create',
            'reward-punishment-update',
            'reward-punishment-delete',
        ],
        'reward-punishment-record' => [
            'reward-punishment-record-view',
            'reward-punishment-record-create',
            'reward-punishment-record-update',
            'reward-punishment-record-delete',
        ],
    ];

    const EMPLOYEE_PERMISSIONS = [
        'employee' => [
            'employee-view',
            'employee-create',
            'employee-update',
            'employee-delete',
            'employee-tab-personal-information-view',
        ],
    ];

    const GRADING_SCHEME_PERMISSIONS = [
        'grading-scheme' => [
            'grading-scheme-view',
            'grading-scheme-create',
            'grading-scheme-update',
            'grading-scheme-delete',
        ],
    ];

    const CONDUCT_SETTING_PERMISSIONS = [
        'conduct-setting' => [
            'conduct-setting-view-all-teacher',
            'conduct-setting-view',
            'conduct-setting-update',
        ],
    ];

    const CONDUCT_RECORD_MANAGEMENT_PERMISSIONS = [
        'conduct-record' => [
            'conduct-record-view',
            'conduct-record-update',
            'conduct-record-delete',
        ],
    ];

    const HOSTEL_PERMISSIONS = [
        'hostel-student' => [
            'hostel-student-view',
            'hostel-student-update'
        ],
        'hostel-person-in-charge' => [
            'hostel-person-in-charge-admin-view',
            'hostel-person-in-charge-admin-add',
            'hostel-person-in-charge-admin-remove',
        ],
        'hostel-block' => [
            'hostel-block-view',
            'hostel-block-create',
            'hostel-block-update',
            'hostel-block-delete',
        ],
        'hostel-room' => [
            'hostel-room-view',
            'hostel-room-create',
            'hostel-room-update',
            'hostel-room-delete',
        ],
        'hostel-room-bed' => [
            'hostel-room-bed-view',
            'hostel-room-bed-create',
            'hostel-room-bed-update',
            'hostel-room-bed-delete',
        ],
        'hostel-bed-assignment' => [
            'hostel-student', // For FE to manage menu
            'hostel-employee', // For FE to manage menu
            'hostel-bed-assignment-assign',
            'hostel-bed-assignment-unassign',
            'hostel-bed-assignment-change',
            'hostel-bed-assignment-download-template',
            'hostel-bed-assignment-import',
            'hostel-bed-assignment-bulk-assignment',
        ],
        'hostel-in-out-record' => [
            'hostel-in-out-record-view',
            'hostel-in-out-record-create',
            'hostel-in-out-record-update',
            'hostel-in-out-record-delete',
        ],
        'hostel-merit-demerit-setting' => [
            'hostel-merit-demerit-setting-view',
            'hostel-merit-demerit-setting-create',
            'hostel-merit-demerit-setting-update',
            'hostel-merit-demerit-setting-delete',
        ],
        'hostel-reward-punishment-setting' => [
            'hostel-reward-punishment-setting-view',
            'hostel-reward-punishment-setting-create',
            'hostel-reward-punishment-setting-update',
            'hostel-reward-punishment-setting-delete',
        ],
        'hostel-reward-punishment-record' => [
            'hostel-reward-punishment-record-view',
            'hostel-reward-punishment-record-create',
            'hostel-reward-punishment-record-update',
            'hostel-reward-punishment-record-delete',
        ],
        'hostel-savings-account' => [
            'hostel-savings-account-view',
            'hostel-savings-account-deposit',
            'hostel-savings-account-withdraw',
            'hostel-savings-account-void'
        ],
    ];

    const GUARDIAN_PERMISSIONS = [
        'guardian' => [
            'guardian-view',
        ]
    ];

    const WALLET_PERMISSIONS = [
        'card' => [
            'card-view',
            'card-create',
            'card-update',
            'card-delete',
            'card-download-template',
            'card-import',
            'card-bulk-assignment',
        ],
        'transaction' => [
            'transaction-admin-view',
            'transaction-admin-refund',
            'transaction-view',
            'transaction-create',
        ],
        'wallet' => [
            'wallet-admin-view',
            'wallet-view',
            'wallet-get-balance',
            'wallet-transfer',
            'wallet-deposit',
            'wallet-admin-withdraw',
            'wallet-admin-adjustment'
        ]
    ];

    const COMPREHENSIVE_ASSESSMENT_PERMISSIONS = [
        'comprehensive-assessment-category' => [
            'comprehensive-assessment-category-view',
            'comprehensive-assessment-category-create',
            'comprehensive-assessment-category-update',
            'comprehensive-assessment-category-delete',
        ],

        'comprehensive-assessment-question' => [
            'comprehensive-assessment-question-view',
            'comprehensive-assessment-question-create',
            'comprehensive-assessment-question-update',
            'comprehensive-assessment-question-delete',
        ],

        'comprehensive-assessment-record' => [
            'comprehensive-assessment-record-view',
            'comprehensive-assessment-record-update',
        ],
    ];

    const LEADERSHIP_POSITION_PERMISSIONS = [
        'leadership-position-record' => [
            'leadership-position-record-view',
            'leadership-position-record-create',
        ],
    ];

    const ROLE_PERMISSIONS = [
        'role' => [
            'role-view',
            'role-create',
            'role-update',
            'role-delete',
        ],
    ];

    const COMPETITION_PERMISSIONS = [
        'competition' => [
            'competition-view',
            'competition-create',
            'competition-update',
            'competition-delete',
        ],
    ];

    const ACCOUNTING_PERMISSIONS = [
        'fees' => [
            'fees-create-and-assign',
            'fees-unpaid-item-view',    // FE access
            'fees-unpaid-item-admin-view',      // BO access
            'fees-unpaid-item-admin-pay', // BO access
            'fees-unpaid-item-pay',    // FE access
        ],
        'posting' => [
            'post-billing-documents-to-autocount'
        ]
    ];

    const ANNOUNCEMENT_PERMISSIONS = [
        'announcement-group' => [
            'announcement-group-view',
            'announcement-group-create',
            'announcement-group-update',
            'announcement-group-delete',
        ],
        'announcement' => [
            'announcement-view',
            'announcement-create',
            'announcement-approve',
            'announcement-delete',
        ],
        // public feature shouldn't need it
        //        'inbox' => [
        //            'inbox-view',
        //            'inbox-update'
        //        ]
    ];

    const REPORT_PERMISSIONS = [
        'wallet' => [
            'wallet-report',
        ],
        'hostel' => [
            'hostel-by-boarders-name-list-report',
            'hostel-by-available-bed-report',
            'hostel-by-checkout-record-report',
            'hostel-by-boarders-list-info-report',
            'hostel-by-boarders-contact-info-report',
            'hostel-by-boarders-date-of-birth-report',
            'hostel-by-boarders-stayback-report',
            'hostel-by-employee-lodging-report',
            'hostel-by-boarders-go-home-or-out-report',
            'hostel-by-change-room-record-report',
            'hostel-report-reward-punishment-by-block',
            'hostel-report-reward-punishment-by-student',
            'hostel-report-reward-punishment-by-room',
        ],
        'card' => [
            'card-report',
        ],
        'attendance' => [
            'attendance-by-summary-list-report',
            'class-attendance-report',
            'student-attendance-report',
            'contractor-daily-attendance-report',
            'student-absent-report',
            'attendance-by-student-attendance-mark-deduction-report',
            'class-attendance-taking-status-report',
        ],
        'billing-document' => [
            'billing-document-by-daily-collection-report',
        ],
        'ecommerce' => [
            'ecommerce-bookshops-orders-report',
            'ecommerce-bookshops-classes-report',
            'ecommerce-bookshops-students-report',
            'ecommerce-order-items-bookshops-report',
            'ecommerce-canteens-classes-report',
            'ecommerce-canteens-classes-weekly-report',
            'ecommerce-canteens-classes-date-range-report',
            'ecommerce-canteens-merchants-report',
            'ecommerce-canteens-by-student-report',
            'ecommerce-canteens-by-merchant-daily-sales-report',
            'ecommerce-canteens-by-daily-sales-group-by-merchant-report',
            'ecommerce-canteens-by-daily-collection-report'
        ],
        'semester-class' => [
            'semester-class-student-contacts-report',
            'semester-class-student-details-report',
            'semester-class-homeroom-teachers-report',
            'semester-class-by-students-in-class-report',
            'non-primary-semester-class-by-students-in-class-report',
        ],
        'library-report' => [
            'library-book-loans-report',
            'library-top-borrowers-report',
            'library-top-borrowed-books-report',
            'library-school-rate-borrow-books-report',
            'library-book-borrow-records-report'
        ],
        'cocurriculum-report' => [
            'cocurriculum-trainer-detail-report',
            'cocurriculum-student-statistic-by-semester-report',
        ],
        'academy-report' => [
            'academy-transferred-student-list',
            'academy-student-analysis-report',
            'academy-student-merit-exceptional-performance-report',
            'examination-result-by-student-report',
            'academy-examination-result-by-semester-class-report',
            'net-average-passing-rate-report',
            'subject-passing-rate-report',
            'subject-average-mark-report',
            'subject-analysis-data-report'
        ],
        'accounting-report' => [
            'accounting-student-outstanding-balance-report',
        ],
        'exam-report' => [
            'examination-result-by-exam-report',
        ],
        'conduct' => [
            'student-conduct-report'
        ],
        'enrollment' => [
            'student-registration-report',
            'enrollment-by-daily-collection-report',
        ],
        'student-affairs-report' => [
            'student-affairs-student-performance-by-date-range-report',
        ],
    ];

    const BILLING_DOCUMENT_PERMISSIONS = [
        'billing-document' => [
            'billing-document-manual-payment',
            'billing-document-admin-view', // For BO
            'billing-document-admin-status-update', // For BO
            'billing-document-view', // For FE
            'billing-document-void', // For FE
            'billing-document-make-payment', // For FE
        ]
    ];

    const SIDE_NAV_PERMISSIONS = [
        'merchant' => [
            'view-all-merchant',
        ],
        'ecommerce' => [
            'bookstore-view',
            'canteen-view',
        ],
    ];

    const DEADLINE_PERMISSIONS = [
        'deadline' => [
            'conduct-deadline-view',
            'conduct-deadline-update',
        ],
    ];

    const STUDENT_SOCIETY_POSITION_PERMISSIONS = [
        'student-society-position' => [
            'student-society-position-update',
        ]
    ];

    const SUBSTITUTE_RECORD_PERMISSIONS = [
        'substitute-record' => [
            'substitute-record-view',
            'substitute-record-bulk-create-update-view',
            'substitute-record-bulk-create-update',
            'substitute-record-update',
            'substitute-record-delete',
        ],
    ];

    const SCHOLARSHIP_PERMISSIONS = [
        'scholarship' => [
            'scholarship-admin-view',
            'scholarship-admin-create',
            'scholarship-admin-update',
            'scholarship-admin-delete',
        ],
    ];

    const EXAM_RESULTS_PERMISSIONS = [
        'exam' => [
            'exam-view',
            'exam-create',
            'exam-update',
            'exam-delete',
        ],
        'promotion-mark' => [
            'promotion-mark-view',
            'promotion-mark-create',
        ],

        'exam-results' => [
            'exam-results-data-entry-view',
            'exam-results-data-entry-view-all',
            'exam-results-data-entry-save',
            'exam-results-data-entry-reopen',
        ],
        'exam-exemptions' => [
            'exam-exemption-save'
        ],
        'exam-postings' => [
            'exam-posting-create-posting-session'
        ],
        'results-posting-header' => [
            'results-posting-header-view'
        ],
        'student-report-card' => [
            'student-report-card-view'
        ],
        'report-card-by-morph' => [
            'report-card-by-morph-view'
        ],
        'grading-framework' => [
            'grading-framework-view',
            'grading-framework-create',
            'grading-framework-update',
            'grading-framework-apply'
        ],
        'exam-semester-setting' => [
            'exam-semester-setting-view',
            'exam-semester-setting-create'
        ]
    ];

    const DISCOUNT_PERMISSIONS = [
        'discount' => [
            'discount-confirm',
            'discount-view',
            'discount-create',
            'discount-update',
        ],
    ];

    // Put all permissions for student role here
    // for mobile app
    const STUDENT_ROLE_PERMISSIONS = [
        'wallet-view',
        'wallet-get-balance',
        'wallet-transfer',
        'wallet-deposit',
        'order-checkout',
        'order-update',
        'order-view',
        'order-cancel',
        'product-category-view',
        'product-view-by-userable',
        'billing-document-view',
        'billing-document-void',
        'billing-document-make-payment',
        'fees-unpaid-item-view',
        'fees-unpaid-item-pay',
        'student-personal-timetable-view',
    ];

    // for mobile app
    const EMPLOYEE_ROLE_PERMISSIONS = [
        'wallet-view',
        'wallet-get-balance',
        'wallet-transfer',
        'wallet-deposit',
        'order-checkout',
        'order-update',
        'order-view',
        'order-cancel',
        'product-category-view',
    ];

    // for mobile app
    const GUARDIAN_ROLE_PERMISSIONS = [
        'wallet-view',
        'wallet-get-balance',
        'wallet-transfer',
        'wallet-deposit',
        'order-checkout',
        'order-update',
        'order-view',
        'order-cancel',
        'product-category-view',
        'enrollment-view',
        'enrollment-create',
        'enrollment-update',
        'enrollment-upload',
        'enrollment-delete-media',
        'billing-document-view',
        'billing-document-void',
        'billing-document-make-payment',
        'fees-unpaid-item-view',
        'fees-unpaid-item-pay',
        'student-personal-timetable-view',
    ];

    const EMPLOYEE_ADMIN_ROLE_PERMISSIONS = [
        'employee-view',
        'employee-create',
        'employee-update',
        'employee-delete',
    ];

    const STUDENT_ADMIN_ROLE_PERMISSIONS = [
        'student-view',
        'student-create',
        'student-update',
        'student-delete',
        'primary-class-view',
        'primary-class-create',
        'primary-class-update',
        'primary-class-delete',
        'english-class-view',
        'english-class-create',
        'english-class-update',
        'english-class-delete',
        'society-class-view',
        'society-class-create',
        'society-class-update',
        'society-class-delete',
        'class-assignment',
        'primary-semester-class-view',
        'primary-semester-class-update',
        'primary-semester-class-delete',
        'english-semester-class-view',
        'english-semester-class-update',
        'english-semester-class-delete',
        'society-semester-class-view',
        'society-semester-class-update',
        'society-semester-class-delete',
    ];

    const GUEST_PERMISSIONS = [
        'guest' => [
            'guest-view',
            'guest-create',
            'guest-update',
            'guest-delete',
        ],
    ];

    const LEAVE_APPLICATION_PERMISSIONS = [
        'leave-application' => [
            'leave-application-view',
            'leave-application-create',
            'leave-application-update',
            'leave-application-delete',
            'leave-application-approve',
        ],
        'leave-application-type' => [
            'leave-application-type-view',
            'leave-application-type-create',
            'leave-application-type-update',
            'leave-application-type-delete',
        ],
    ];

    const ATTENDANCE_PERMISSIONS = [
        'attendance' => [
            'attendance-view',
            'attendance-delete',
            'trigger-attendance-posting',
        ],
        'class-attendance-taking' => [
            'class-attendance-taking-view',
            'class-attendance-taking-bulk-update',
        ],
        'attendance-period' => [
            'attendance-period-override-view',
            'attendance-period-override-create',
            'attendance-period-override-update',
            'attendance-period-override-delete',
            'school-attendance-period-override-view',
            'school-attendance-period-override-create',
            'school-attendance-period-override-update',
            'school-attendance-period-override-delete',
        ],
        'student-timetable' => [
            'student-attendance-period-view',
            'student-personal-timetable-view',
        ],
        'employee-timetables' => [
            'employee-timetable-view',
        ],
        'attendance-input' => [
            'attendance-input-view',
            'student-attendance-input-view',
            'employee-attendance-input-view',
            'contractor-attendance-input-view',
            'attendance-input-create',
            'attendance-input-update',
            'attendance-input-delete',
        ],
    ];

    const CALENDAR_PERMISSIONS = [
        'calendar' => [
            'calendar-view',
            'calendar-create',
            'calendar-update',
            'calendar-delete',
        ],
        'calendar-setting' => [
            'calendar-setting-view',
            'calendar-setting-edit',
            'calendar-setting-delete',
        ],
        'calendar-target' => [
            'calendar-target-view',
            'calendar-target-update',
        ],
    ];

    const TIMESLOT_OVERRIDE_PERMISSIONS = [
        'timeslot-override' => [
            'timeslot-override-view',
            'timeslot-override-create',
            'timeslot-override-update',
            'timeslot-override-delete',
        ],
    ];

    const DUPLICATE_SEMESTER_SETTING_STATUS_PERMISSIONS = [
        'duplicate-semester-setting-status' => [
            'duplicate-semester-setting-statuses-view',
        ],
    ];

    const SUPPORT_ROLE_PERMISSIONS = [
        'master-data-menu-view',
        'master-leave-reason-view',
        'master-employee-category-view',
        'master-health-concern-view',
        'master-bank-view',
        'master-education-view',
        'master-course-view',
        'master-internationalization-view',
        'master-race-view',
        'master-religion-view',
        'master-country-view',
        'master-state-view',
        'master-school-profile-view',
        'master-grade-view',
        'master-config-admin-view',
        'master-config-view',
        'master-semester-year-setting-view',
        'master-semester-setting-view',
        'master-reward-punishment-category-view',
        'master-society-position-view',
        'master-reward-punishment-sub-category-view',
        'master-employee-job-title-view',
        'master-book-sub-classification-view',
        'master-book-source-view',
        'master-book-classification-view',
        'master-book-category-view',
        'master-author-view',
        'master-uom-view',
        'master-gl-account-view',
        'master-currency-view',
        'master-product-view',
        'master-award-view',
        'master-leadership-position-view',
        'master-school-view',
        'master-withdrawal-reason-view',
        'master-payment-method-view',
        'master-employee-session-view',
        'department-view',
        'merchant-view',
        'product-view',
        'product-group-view',
        'product-category-admin-view',
        'product-category-view',
        'product-tag-view',
        'order-view',
        'order-admin-view',
        'period-group-view',
        'period-view',
        'timetable-view',
        'book-view',
        'library-member-view',
        'book-loan-view',
        'book-language-view',
        'pos-terminal-key-view',
        'unpaid-item-assignment-admin-view',
        'terminal-view',
        'user-special-setting-view',
        'user-view',
        'subject-view',
        'primary-class-subject-view',
        'english-class-subject-view',
        'society-class-subject-view',
        'elective-class-subject-view',
        'student-view',
        'student-tab-personal-information-view',
        'student-tab-guardians-view',
        'student-tab-disciplinary-record-view',
        'student-tab-comprehensive-assessment-record-view',
        'student-tab-fee-records-view',
        'enrollment-view',
        'enrollment-session-view',
        'enrollment-user-view',
        'club-category-view',
        'club-view',
        'counselling-case-record-view-all-teacher',
        'counselling-case-record-view',
        'contractor-view',
        'primary-class-view',
        'english-class-view',
        'society-class-view',
        'elective-class-view',
        'class-seat-assignment-view',
        'primary-semester-class-view',
        'english-semester-class-view',
        'society-semester-class-view',
        'elective-semester-class-view',
        'merit-demerit-setting-view',
        'reward-punishment-view',
        'reward-punishment-record-view',
        'employee-view',
        'employee-tab-personal-information-view',
        'grading-scheme-view',
        'conduct-setting-view-all-teacher',
        'conduct-setting-view',
        'conduct-record-view',
        'hostel-student-view',
        'hostel-person-in-charge-admin-view',
        'hostel-block-view',
        'hostel-room-view',
        'hostel-room-bed-view',
        'hostel-student', // For FE to manage menu
        'hostel-employee', // For FE to manage menu
        'hostel-in-out-record-view',
        'hostel-merit-demerit-setting-view',
        'hostel-reward-punishment-setting-view',
        'hostel-reward-punishment-record-view',
        'hostel-savings-account-view',
        'guardian-view',
        'card-view',
        'transaction-admin-view',
        'transaction-view',
        'wallet-admin-view',
        'wallet-view',
        'comprehensive-assessment-category-view',
        'comprehensive-assessment-question-view',
        'comprehensive-assessment-record-view',
        'leadership-position-record-view',
        'role-view',
        'competition-view',
        'fees-unpaid-item-view',
        'fees-unpaid-item-admin-view',
        'announcement-group-view',
        'announcement-view',
        'billing-document-admin-view',
        'billing-document-view',
        'view-all-merchant',
        'conduct-deadline-view',
        'substitute-record-view',
        'substitute-record-bulk-create-update-view',
        'scholarship-admin-view',
        'exam-view',
        'promotion-mark-view',
        'exam-results-data-entry-view',
        'exam-results-data-entry-view-all',
        'results-posting-header-view',
        'student-report-card-view',
        'report-card-by-morph-view',
        'grading-framework-view',
        'exam-semester-setting-view',
        'discount-view',
        'guest-view',
        'leave-application-view',
        'leave-application-type-view',
        'attendance-view',
        'class-attendance-taking-view',
        'attendance-period-override-view',
        'school-attendance-period-override-view',
        'student-attendance-period-view',
        'student-personal-timetable-view',
        'employee-timetable-view',
        'attendance-input-view',
        'student-attendance-input-view',
        'employee-attendance-input-view',
        'contractor-attendance-input-view',
        'calendar-view',
        'calendar-setting-view',
        'calendar-target-view',
        'timeslot-override-view',
        'duplicate-semester-setting-statuses-view',
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (app()->environment('testing')) {
            $this->runTestingSeeder();
        } else {
            $this->runSeeder();
        }
    }

    private function runTestingSeeder(): void
    {
        $superadmin_role = Role::firstOrCreate(['name' => 'Super Admin']);

        $superadmin = User::where('email', '<EMAIL>')->first();

        if ($superadmin) {
            $superadmin->assignRole($superadmin_role);
        }

        $date = now();
        foreach (self::SEEDER_PERMISSIONS as $category => $permissions) {
            foreach ($permissions as $sub_category => $permission) {
                foreach ($permission as $p) {
                    $permissions_to_insert[] = [
                        'name' => $p,
                        'category' => $category,
                        'sub_category' => $sub_category,
                        'guard_name' => 'api',
                        'created_at' => $date,
                        'updated_at' => $date,
                    ];
                }
            }
        }

        Permission::insert($permissions_to_insert);
        $permission_ids = Permission::select('id')->get();

        $role_has_permissions_entries = $permission_ids->map(function ($permission) use ($superadmin_role) {
            return ['permission_id' => $permission->id, 'role_id' => $superadmin_role->id];
        })->toArray();

        $guardian_role = Role::firstOrCreate(['name' => 'Guardian']);

        $guardian = User::where('email', '<EMAIL>')->first();

        if ($guardian) {
            $guardian->assignRole($guardian_role);
        }

        $guardian_permission = Permission::select('id')
            ->where('sub_category', 'enrollment')
            ->whereIn('name', self::STUDENT_PERMISSIONS['enrollment'])
            ->get();

        foreach ($guardian_permission as $permission) {
            $role_has_permissions_entries[] = [
                'permission_id' => $permission->id,
                'role_id' => $guardian_role->id
            ];
        }

        DB::table('role_has_permissions')->insert($role_has_permissions_entries);
    }

    private function runSeeder(): void
    {
        $superadmin_role = Role::firstOrCreate(['name' => 'Super Admin']);

        $superadmin = User::where('email', '<EMAIL>')->first();

        if ($superadmin) {
            $superadmin->assignRole($superadmin_role);
        }

        foreach (self::SEEDER_PERMISSIONS as $category => $permissions) {
            foreach ($permissions as $sub_category => $permission) {
                foreach ($permission as $p) {
                    $permission = Permission::updateOrCreate(
                        [
                            'name' => $p,
                        ],
                        [
                            'name' => $p,
                            'category' => $category,
                            'sub_category' => $sub_category,
                        ],
                    );

                    $superadmin_role->givePermissionTo($permission);
                }
            }
        }

        $this->call(SystemRoleSeeder::class);
        $this->assignPermissionsToStudentRole();
        $this->assignPermissionsToEmployeeRole();
        $this->assignPermissionsToGuardianRole();
        $this->assignPermissionsToEmployeeAdminRole();
        $this->assignPermissionsToStudentAdminRole();
        $this->assignPermissionsToSupportRole();
    }

    private function assignPermissionsToStudentRole()
    {
        $student_role = Role::where('name', Role::STUDENT)->firstOrFail();

        $student_role->givePermissionTo(Permission::whereIn('name', self::STUDENT_ROLE_PERMISSIONS)->get());

        $students = Student::with('user')->whereHas('user')->lazy();

        foreach ($students as $student) {
            $student->user->assignRole($student_role);
        }
    }

    private function assignPermissionsToEmployeeRole()
    {
        $employee_role = Role::where('name', Role::EMPLOYEE)->firstOrFail();

        $employee_role->givePermissionTo(Permission::whereIn('name', self::EMPLOYEE_ROLE_PERMISSIONS)->get());

        $employees = Employee::with('user')->whereHas('user')->lazy();

        foreach ($employees as $employee) {
            $employee->user->assignRole($employee_role);
        }
    }

    private function assignPermissionsToGuardianRole()
    {
        $guardian_role = Role::where('name', Role::GUARDIAN)->firstOrFail();

        $guardian = User::where('email', '<EMAIL>')->first();

        if ($guardian) {
            $guardian->assignRole($guardian_role);
        }

        $guardian_permission = Permission::where('sub_category', 'enrollment')
            ->whereIn('name', self::GUARDIAN_ROLE_PERMISSIONS)
            ->get();

        foreach ($guardian_permission as $p) {
            $guardian_role->givePermissionTo($p);
        }

        $guardians = Guardian::with('user')->whereHas('user')->lazy();

        foreach ($guardians as $guardian) {
            $guardian->user->assignRole($guardian_role);
        }
    }

    private function assignPermissionsToEmployeeAdminRole()
    {
        $employee_admin_role = Role::where('name', Role::EMPLOYEE_ADMIN)->firstOrFail();

        $employee_admin_role->givePermissionTo(Permission::whereIn('name', self::EMPLOYEE_ADMIN_ROLE_PERMISSIONS)->get());
    }

    private function assignPermissionsToStudentAdminRole()
    {
        $student_admin_role = Role::where('name', Role::STUDENT_ADMIN)->firstOrFail();

        $student_admin_role->givePermissionTo(Permission::whereIn('name', self::STUDENT_ADMIN_ROLE_PERMISSIONS)->get());
    }

    private function assignPermissionsToSupportRole()
    {
        $support_role = Role::where('name', Role::SUPPORT)->firstOrFail();

        $support_role_permission = array_merge(
            array_merge(...array_values(self::REPORT_PERMISSIONS)),
            self::SUPPORT_ROLE_PERMISSIONS
        );

        $support_role->givePermissionTo(Permission::whereIn('name', array_unique($support_role_permission))->get());
    }
}
